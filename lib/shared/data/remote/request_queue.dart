import 'dart:async';
import 'dart:collection';

import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:synchronized/synchronized.dart';

/// Request priority levels
enum RequestPriority {
  high,
  normal,
  low,
}

/// A request item in the queue
class RequestItem<T> {
  final Future<T> Function() request;
  final RequestPriority priority;
  final String? tag;
  final Completer<T> completer;

  RequestItem({
    required this.request,
    this.priority = RequestPriority.normal,
    this.tag,
  }) : completer = Completer<T>();
}

/// Handle HTTP request queue with priority support and concurrency control
class RequestQueue {
  static const int _defaultMaxQueueSize = 100;
  static const int _defaultMaxConcurrency = 1;

  final int maxQueueSize;

  final Queue<RequestItem> _queue = Queue<RequestItem>();
  int _activeRequests = 0;
  bool _isCancelled = false;
  bool _isPaused = false;
  final Lock _lock = Lock();

  // Error handler
  void Function(Object error, StackTrace stackTrace)? onError;

  // Token refresh callback
  void Function()? onTokenRefreshNeeded;

  RequestQueue({
    this.maxQueueSize = _defaultMaxQueueSize,
    this.onError,
  });

  /// Add a request to the queue
  /// Returns a Future that completes when the request is executed
  Future<T> addRequest<T>(
    Future<T> Function() request, {
    RequestPriority priority = RequestPriority.normal,
    String? tag,
  }) async {
    final item = RequestItem<T>(
      request: request,
      priority: priority,
      tag: tag,
    );

    await _lock.synchronized(() async {
      if (_queue.length >= maxQueueSize) {
        throw Exception('Request queue is full');
      }

      _addItemByPriority(item);
      _processQueue();
    });

    return item.completer.future;
  }

  void _addItemByPriority(RequestItem item) {
    if (_queue.isEmpty) {
      _queue.add(item);
      return;
    }

    // Convert to list for easier manipulation
    final list = _queue.toList();
    _queue.clear();

    // Find insertion point based on priority
    int insertIndex = list.length;
    for (int i = 0; i < list.length; i++) {
      if (item.priority.index < list[i].priority.index) {
        insertIndex = i;
        break;
      }
    }

    list.insert(insertIndex, item);
    _queue.addAll(list);
  }

  void _processQueue() {
    while (_queue.isNotEmpty &&
        _activeRequests < _defaultMaxConcurrency &&
        !_isCancelled &&
        !_isPaused) {
      LogUtils.d(
        'Processing queue. Queue size: ${_queue.length}, Active: $_activeRequests, Paused: $_isPaused, Cancelled: $_isCancelled',
        tag: 'RequestQueue',
      );
      final item = _queue.removeFirst();
      _activeRequests++;

      _executeRequest(item);
    }
  }

  void _executeRequest(RequestItem item) {
    item.request().then((result) {
      if (!item.completer.isCompleted) {
        item.completer.complete(result);
      }
    }).catchError((error, stackTrace) {
      onError?.call(error, stackTrace);
      if (!item.completer.isCompleted) {
        item.completer.completeError(error, stackTrace);
      }
    }).whenComplete(() {
      // Schedule the cleanup to avoid potential deadlock
      Future.microtask(() async {
        await _lock.synchronized(() {
          _activeRequests--;
          if (!_isCancelled) {
            _processQueue();
          }
        });
      });
    });
  }

  void cancel() {
    _lock.synchronized(() {
      _isCancelled = true;
      // Complete all pending requests with cancellation error
      while (_queue.isNotEmpty) {
        final item = _queue.removeFirst();
        if (!item.completer.isCompleted) {
          item.completer.completeError(
            Exception('Request cancelled'),
            StackTrace.current,
          );
        }
      }
    });
  }

  void clear() {
    _lock.synchronized(() {
      while (_queue.isNotEmpty) {
        final item = _queue.removeFirst();
        if (!item.completer.isCompleted) {
          item.completer.completeError(
            Exception('Request queue cleared'),
            StackTrace.current,
          );
        }
      }
    });
  }

  void removeByTag(String tag) {
    _lock.synchronized(() {
      final itemsToRemove = <RequestItem>[];
      for (final item in _queue) {
        if (item.tag == tag) {
          itemsToRemove.add(item);
        }
      }
      for (final item in itemsToRemove) {
        _queue.remove(item);
        if (!item.completer.isCompleted) {
          item.completer.completeError(
            Exception('Request removed by tag: $tag'),
            StackTrace.current,
          );
        }
      }
    });
  }

  /// Pause the queue processing
  void pause() {
    _lock.synchronized(() {
      _isPaused = true;
    });
  }

  /// Resume the queue processing
  void resume() {
    _lock.synchronized(() {
      _isPaused = false;
      LogUtils.d(
        'Resuming queue processing. Queue size: ${_queue.length}, Active: $_activeRequests, Paused: $_isPaused, Cancelled: $_isCancelled',
        tag: 'RequestQueue',
      );
      if (!_isCancelled) {
        _processQueue();
      }
    });
  }

  int get queueSize => _queue.length;
  int get activeRequests => _activeRequests;
  bool get isEmpty => _queue.isEmpty;
  bool get isCancelled => _isCancelled;
  bool get isPaused => _isPaused;
}
